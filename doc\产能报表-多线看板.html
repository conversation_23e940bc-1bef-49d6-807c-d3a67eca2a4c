<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多线产能看板 (Multi-Line Capacity Dashboard)</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body { font-family: sans-serif; margin: 20px; background-color: #f0f2f5; }
        h1 { text-align: center; color: #333; }
        .dashboard-container { display: flex; flex-direction: column; gap: 40px; }
        .line-report-container { 
            background-color: #fff; 
            border: 1px solid #ddd; 
            border-radius: 8px; 
            padding: 20px; 
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .line-title { color: #1a73e8; border-bottom: 2px solid #eee; padding-bottom: 10px; margin-bottom: 20px; }
        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        th, td { border: 1px solid #ddd; padding: 10px; text-align: center; }
        th { background-color: #f8f9fa; }
        .status-met { color: #28a745; font-weight: bold; }
        .status-not-met { color: #dc3545; font-weight: bold; }
        .chart-container { max-height: 300px; }
        .filters { text-align: center; margin-bottom: 30px; padding: 15px; background: #fff; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
    </style>
</head>
<body>

    <h1>多线产能看板 (Multi-Line Capacity Dashboard)</h1>

    <div class="filters">
        <label for="date-picker">日期 (Date):</label>
        <input type="date" id="date-picker" value="2025-07-15">
        <button onclick="updateAllReports()">刷新全部 (Refresh All)</button>
    </div>

    <div id="dashboard-container" class="dashboard-container">
        <!-- All line reports will be dynamically inserted here -->
    </div>

    <script>
        const sampleData = {
            "LINE-A": [
              {"hour": 8, "plannedQuantity": 1000, "actualQuantity": 1050, "achievementRate": "105%", "status": "达标", "reason": null},
              {"hour": 9, "plannedQuantity": 1200, "actualQuantity": 900, "achievementRate": "75%", "status": "未达标", "reason": "设备故障30分钟"},
              {"hour": 10, "plannedQuantity": 1200, "actualQuantity": 1180, "achievementRate": "98%", "status": "未达标", "reason": "物料延迟"},
              {"hour": 11, "plannedQuantity": 1200, "actualQuantity": 1250, "achievementRate": "104%", "status": "达标", "reason": null},
              {"hour": 12, "plannedQuantity": 800, "actualQuantity": 810, "achievementRate": "101%", "status": "达标", "reason": null},
              {"hour": 13, "plannedQuantity": 1500, "actualQuantity": 1450, "achievementRate": "97%", "status": "未达标", "reason": "人手不足"},
              {"hour": 14, "plannedQuantity": 1500, "actualQuantity": 1550, "achievementRate": "103%", "status": "达标", "reason": null},
              {"hour": 15, "plannedQuantity": 1500, "actualQuantity": 1600, "achievementRate": "107%", "status": "达标", "reason": null}
            ],
            "LINE-B": [
              {"hour": 8, "plannedQuantity": 950, "actualQuantity": 940, "achievementRate": "99%", "status": "未达标", "reason": "机器预热"},
              {"hour": 9, "plannedQuantity": 1100, "actualQuantity": 1120, "achievementRate": "102%", "status": "达标", "reason": null},
              {"hour": 10, "plannedQuantity": 1100, "actualQuantity": 1050, "achievementRate": "95%", "status": "未达标", "reason": "品质异常"},
              {"hour": 11, "plannedQuantity": 1100, "actualQuantity": 1110, "achievementRate": "101%", "status": "达标", "reason": null},
              {"hour": 12, "plannedQuantity": 750, "actualQuantity": 750, "achievementRate": "100%", "status": "达标", "reason": null},
              {"hour": 13, "plannedQuantity": 1400, "actualQuantity": 1420, "achievementRate": "101%", "status": "达标", "reason": null},
              {"hour": 14, "plannedQuantity": 1400, "actualQuantity": 1380, "achievementRate": "99%", "status": "未达标", "reason": "短暂停线"},
              {"hour": 15, "plannedQuantity": 1400, "actualQuantity": 1450, "achievementRate": "104%", "status": "达标", "reason": null}
            ],
            "LINE-C (Backup)": [
              {"hour": 8, "plannedQuantity": 500, "actualQuantity": 510, "achievementRate": "102%", "status": "达标", "reason": null},
              {"hour": 9, "plannedQuantity": 500, "actualQuantity": 480, "achievementRate": "96%", "status": "未达标", "reason": "新员工培训"},
              {"hour": 10, "plannedQuantity": 500, "actualQuantity": 500, "achievementRate": "100%", "status": "达标", "reason": null},
              {"hour": 11, "plannedQuantity": 500, "actualQuantity": 520, "achievementRate": "104%", "status": "达标", "reason": null}
            ]
        };

        function createLineReportSection(lineId, data) {
            const container = document.createElement('div');
            container.className = 'line-report-container';
            container.id = `report-${lineId}`;

            // Title
            const title = document.createElement('h2');
            title.className = 'line-title';
            title.innerText = `产线 (Line): ${lineId}`;
            container.appendChild(title);

            // Chart
            const chartContainer = document.createElement('div');
            chartContainer.className = 'chart-container';
            const canvas = document.createElement('canvas');
            canvas.id = `chart-${lineId}`;
            chartContainer.appendChild(canvas);
            container.appendChild(chartContainer);

            // Table
            const table = document.createElement('table');
            table.innerHTML = `
                <thead>
                    <tr>
                        <th>小时 (Hour)</th>
                        <th>计划产能 (Planned)</th>
                        <th>实际产能 (Actual)</th>
                        <th>达成率 (Rate)</th>
                        <th>状态 (Status)</th>
                        <th>未达成原因 (Reason)</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Data goes here -->
                </tbody>
            `;
            container.appendChild(table);
            
            // Populate table rows
            const tableBody = table.querySelector("tbody");
            data.forEach(row => {
                const tr = document.createElement("tr");
                const statusClass = row.status === '达标' ? 'status-met' : 'status-not-met';
                tr.innerHTML = `
                    <td>${row.hour}</td>
                    <td>${row.plannedQuantity}</td>
                    <td>${row.actualQuantity}</td>
                    <td>${row.achievementRate}</td>
                    <td class="${statusClass}">${row.status}</td>
                    <td>${row.reason || 'N/A'}</td>
                `;
                tableBody.appendChild(tr);
            });

            return container;
        }

        function renderLineChart(lineId, data) {
            const ctx = document.getElementById(`chart-${lineId}`).getContext('2d');
            const labels = data.map(d => `${d.hour}:00`);
            const plannedData = data.map(d => d.plannedQuantity);
            const actualData = data.map(d => d.actualQuantity);

            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [
                        {
                            label: '计划产能 (Planned)',
                            data: plannedData,
                            borderColor: 'rgb(54, 162, 235)',
                            backgroundColor: 'rgba(54, 162, 235, 0.2)',
                            fill: true,
                            tension: 0.2,
                        },
                        {
                            label: '实际产能 (Actual)',
                            data: actualData,
                            borderColor: 'rgb(255, 99, 132)',
                            backgroundColor: 'rgba(255, 99, 132, 0.2)',
                            fill: true,
                            tension: 0.2,
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        title: {
                            display: true,
                            text: '计划 vs 实际产能 (Planned vs. Actual Capacity)'
                        }
                    },
                    scales: {
                        y: { beginAtZero: true }
                    }
                }
            });
        }

        function updateAllReports() {
            const dashboardContainer = document.getElementById('dashboard-container');
            dashboardContainer.innerHTML = ''; // Clear previous reports
            
            // In a real app, you'd fetch new data based on the selected date
            const selectedDate = document.getElementById('date-picker').value;
            console.log(`Fetching data for ${selectedDate}...`);

            for (const lineId in sampleData) {
                if (sampleData.hasOwnProperty(lineId)) {
                    const lineData = sampleData[lineId];
                    const reportSection = createLineReportSection(lineId, lineData);
                    dashboardContainer.appendChild(reportSection);
                    // Must wait for the canvas to be in the DOM before rendering chart
                    setTimeout(() => renderLineChart(lineId, lineData), 0);
                }
            }
        }

        // Initial load
        document.addEventListener('DOMContentLoaded', updateAllReports);
    </script>

</body>
</html>
