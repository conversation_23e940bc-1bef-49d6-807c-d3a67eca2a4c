<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多线产能看板 Vue 组件演示</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        .content {
            padding: 40px;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin: 30px 0;
        }
        .feature-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 25px;
            border-left: 4px solid #4CAF50;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        .feature-card h3 {
            color: #333;
            margin-top: 0;
            font-size: 1.3em;
        }
        .feature-card p {
            color: #666;
            line-height: 1.6;
        }
        .tech-stack {
            background: #e3f2fd;
            border-radius: 8px;
            padding: 25px;
            margin: 30px 0;
        }
        .tech-stack h3 {
            color: #1976d2;
            margin-top: 0;
        }
        .tech-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 15px;
        }
        .tech-tag {
            background: #2196F3;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 500;
        }
        .code-section {
            background: #f5f5f5;
            border-radius: 8px;
            padding: 25px;
            margin: 30px 0;
            border: 1px solid #e0e0e0;
        }
        .code-section h3 {
            color: #333;
            margin-top: 0;
        }
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 6px;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            line-height: 1.5;
        }
        .highlight {
            color: #68d391;
        }
        .string {
            color: #fbb6ce;
        }
        .comment {
            color: #a0aec0;
            font-style: italic;
        }
        .file-structure {
            background: #fff3e0;
            border-radius: 8px;
            padding: 25px;
            margin: 30px 0;
        }
        .file-structure h3 {
            color: #f57c00;
            margin-top: 0;
        }
        .file-tree {
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            line-height: 1.8;
            color: #333;
        }
        .file-tree .folder {
            color: #f57c00;
            font-weight: bold;
        }
        .file-tree .file {
            color: #666;
        }
        .demo-link {
            background: linear-gradient(135deg, #FF6B6B 0%, #ee5a52 100%);
            color: white;
            padding: 15px 30px;
            border-radius: 25px;
            text-decoration: none;
            display: inline-block;
            margin: 20px 0;
            font-weight: 500;
            transition: transform 0.3s ease;
        }
        .demo-link:hover {
            transform: translateY(-2px);
            text-decoration: none;
            color: white;
        }
        .status-badge {
            background: #4CAF50;
            color: white;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 500;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>多线产能看板 Vue 组件</h1>
            <p>基于 PrimeVue + ECharts 的现代化产能监控解决方案</p>
            <span class="status-badge">✅ 已完成</span>
        </div>
        
        <div class="content">
            <div class="feature-grid">
                <div class="feature-card">
                    <h3>🎨 现代化UI设计</h3>
                    <p>使用 PrimeVue 组件库，支持明暗主题切换，响应式设计适配各种屏幕尺寸，提供优雅的用户体验。</p>
                </div>
                
                <div class="feature-card">
                    <h3>📊 交互式图表</h3>
                    <p>基于 ECharts 的高性能图表，支持缩放、悬停提示、动画效果，直观展示计划与实际产能对比。</p>
                </div>
                
                <div class="feature-card">
                    <h3>📅 灵活的时间选择</h3>
                    <p>集成日期选择器，支持查看任意日期的产能数据，一键刷新获取最新信息。</p>
                </div>
                
                <div class="feature-card">
                    <h3>📋 详细数据表格</h3>
                    <p>每个产线配备详细的数据表格，显示小时级产能数据、达成率、状态标签和未达成原因。</p>
                </div>
                
                <div class="feature-card">
                    <h3>🔌 API 集成</h3>
                    <p>完整的 TypeScript API 接口定义，支持真实数据获取，包含错误处理和加载状态。</p>
                </div>
                
                <div class="feature-card">
                    <h3>🎯 类型安全</h3>
                    <p>完整的 TypeScript 类型定义，确保代码质量和开发体验，减少运行时错误。</p>
                </div>
            </div>
            
            <div class="tech-stack">
                <h3>🛠️ 技术栈</h3>
                <div class="tech-list">
                    <span class="tech-tag">Vue 3</span>
                    <span class="tech-tag">TypeScript</span>
                    <span class="tech-tag">PrimeVue</span>
                    <span class="tech-tag">ECharts</span>
                    <span class="tech-tag">Vue-ECharts</span>
                    <span class="tech-tag">Date-fns</span>
                    <span class="tech-tag">Composition API</span>
                    <span class="tech-tag">Vite</span>
                </div>
            </div>
            
            <div class="file-structure">
                <h3>📁 文件结构</h3>
                <div class="file-tree">
<span class="folder">src/pages/capacity/</span><br>
├── <span class="file">MultiLineCapacityDashboard.vue</span>  # 主组件<br>
├── <span class="folder">components/</span><br>
│   └── <span class="file">CapacityChart.vue</span>           # 图表组件<br>
└── <span class="file">index.vue</span>                       # 入口页面<br>
<br>
<span class="folder">src/api/capacity/</span><br>
├── <span class="file">index.ts</span>                        # API接口<br>
└── <span class="file">types.ts</span>                        # 类型定义<br>
                </div>
            </div>
            
            <div class="code-section">
                <h3>💻 使用示例</h3>
                <div class="code-block">
<span class="comment">// 在 Vue 组件中使用</span><br>
<span class="highlight">&lt;template&gt;</span><br>
&nbsp;&nbsp;<span class="highlight">&lt;MultiLineCapacityDashboard /&gt;</span><br>
<span class="highlight">&lt;/template&gt;</span><br>
<br>
<span class="highlight">&lt;script setup&gt;</span><br>
<span class="highlight">import</span> MultiLineCapacityDashboard <span class="highlight">from</span> <span class="string">'~/pages/capacity/MultiLineCapacityDashboard.vue'</span><br>
<span class="highlight">&lt;/script&gt;</span>
                </div>
            </div>
            
            <div class="code-section">
                <h3>🔗 API 接口</h3>
                <div class="code-block">
<span class="comment">// 获取多线产能数据</span><br>
<span class="highlight">const</span> data = <span class="highlight">await</span> capacityApi.getMultiLineCapacityData({<br>
&nbsp;&nbsp;date: <span class="string">'2025-07-15'</span><br>
})<br>
<br>
<span class="comment">// 获取产能统计</span><br>
<span class="highlight">const</span> stats = <span class="highlight">await</span> capacityApi.getMultiLineCapacityStatistics({<br>
&nbsp;&nbsp;date: <span class="string">'2025-07-15'</span><br>
})
                </div>
            </div>
            
            <a href="/capacity/multi-line-dashboard" class="demo-link">
                🚀 查看在线演示
            </a>
            
            <div style="margin-top: 40px; padding-top: 30px; border-top: 1px solid #eee; color: #666; text-align: center;">
                <p>✨ 组件已完成开发，支持生产环境部署</p>
                <p>📝 详细文档请参考：<code>doc/多线产能看板使用说明.md</code></p>
            </div>
        </div>
    </div>
</body>
</html>
