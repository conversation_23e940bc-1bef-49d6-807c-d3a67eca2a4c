<script setup lang="ts">
import Toast from 'primevue/toast'
import ConfirmPopup from 'primevue/confirmpopup'
import { useLayoutStore } from './stores/layout'

useDark()
useLayoutStore().init()
// const router = useRouter()
// const { needLogin, needRefresh, refreshToken, accessToken } = useToken()

// onBeforeMount(async () => {
//   if (needLogin()) {
//     router.push(loginPath)
//   }
//   if (needRefresh() && refreshToken.value) {
//     try {
//       accessToken.value = await authApi.refresh(refreshToken.value)
//     }
//     catch {
//       router.push(loginPath)
//     }
//   }
// })
</script>

<template>
  <Toast />
  <ConfirmPopup group="confirm">
    <template #container="{ message, acceptCallback, rejectCallback }">
      <div class="rounded-lg p-6 shadow-lg">
        <div class="mb-4 flex items-center gap-3 text-lg">
          <i class="pi pi-exclamation-circle text-yellow-500" />
          <span>{{ message.message }}</span>
        </div>
        <div class="flex items-center justify-end gap-3">
          <Button
            icon="pi pi-check"
            severity="success"
            size="small"
            label="确认"
            @click="acceptCallback"
          />
          <Button
            icon="pi pi-times"
            outlined
            severity="secondary"
            size="small"
            label="取消"
            @click="rejectCallback"
          />
        </div>
      </div>
    </template>
  </ConfirmPopup>
  <ConfirmPopup group="delete">
    <template #container="{ message, acceptCallback, rejectCallback }">
      <div class="rounded-lg p-6 shadow-lg">
        <div class="mb-4 flex items-center gap-3 text-lg">
          <i class="pi pi-exclamation-circle text-yellow-500" />
          <span>{{ message.message }}</span>
        </div>
        <div class="flex items-center justify-end gap-3">
          <Button
            icon="pi pi-check"
            severity="danger"
            size="small"
            label="确认"
            @click="acceptCallback"
          />
          <Button
            icon="pi pi-times"
            outlined
            severity="secondary"
            size="small"
            label="取消"
            @click="rejectCallback"
          />
        </div>
      </div>
    </template>
  </ConfirmPopup>
  <RouterView />
</template>
