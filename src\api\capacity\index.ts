import type { CapacityReportWithLine } from './type'
import type {
  CapacityQueryParams,
  MultiLineCapacityData,
  MultiLineCapacityStatistics,
} from './types'
import { kyGet, kyPost } from '~/utils/request'

export const capacityApi = {
  /**
   * Get capacity report data by date
   * @param workDate Work date in YYYY-MM-DD format
   * @returns List of capacity reports with line information
   */
  getReport: (workDate: string) => kyGet('capacity/report', { workDate }).json<CapacityReportWithLine[]>(),

  // 获取多线产能数据
  getMultiLineCapacityData: (params: CapacityQueryParams) =>
    kyPost('capacity/multi-line/data', params).json<MultiLineCapacityData>(),

  // 获取多线产能统计
  getMultiLineCapacityStatistics: (params: CapacityQueryParams) =>
    kyPost('capacity/multi-line/statistics', params).json<MultiLineCapacityStatistics>(),

  // 获取可用产线列表
  getAvailableLines: () =>
    kyGet('capacity/lines').json<Array<{ lineId: string, lineName: string }>>(),
}
