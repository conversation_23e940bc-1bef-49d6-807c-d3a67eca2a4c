// 多线产能看板相关类型定义

// 小时产能数据
export interface HourlyCapacityData {
  hour: number
  plannedQuantity: number
  actualQuantity: number
  achievementRate: string
  status: '达标' | '未达标'
  reason: string | null
}

// 产线数据
export interface LineCapacityData {
  lineId: string
  lineName: string
  data: HourlyCapacityData[]
}

// 多线产能看板数据
export interface MultiLineCapacityData {
  date: string
  lines: LineCapacityData[]
}

// 查询参数
export interface CapacityQueryParams {
  date: string
  lineIds?: string[]
}

// 产能统计数据
export interface CapacityStatistics {
  totalPlanned: number
  totalActual: number
  overallAchievementRate: string
  achievedHours: number
  totalHours: number
}

// 产线统计数据
export interface LineStatistics extends CapacityStatistics {
  lineId: string
  lineName: string
}

// 多线产能统计响应
export interface MultiLineCapacityStatistics {
  date: string
  overall: CapacityStatistics
  lines: LineStatistics[]
}
