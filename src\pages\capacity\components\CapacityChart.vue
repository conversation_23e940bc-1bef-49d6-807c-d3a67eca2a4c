<script setup lang="ts">
import { useDark } from '@vueuse/core'
import { computed } from 'vue'
import type { CapacityReportDto } from '~/api/capacity/type'
import { responsivePx } from '~/utils/responsive'

const props = defineProps<{
  lineId: string
  data: CapacityReportDto[]
}>()

const isDark = useDark()

// 生成图表配置
const chartOption = computed(() => {
  const textColor = isDark.value ? '#E5EAF3' : '#666'
  const splitLineColor = isDark.value ? 'rgba(84,89,104,0.3)' : '#DDD'
  const axisLineColor = isDark.value ? '#626675' : '#999'

  const labels = props.data.map(d => `${d.hour}:00`)
  const plannedData = props.data.map(d => d.plannedQuantity)
  const actualData = props.data.map(d => d.actualQuantity)

  const hasData = props.data.length > 0

  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'cross' },
      backgroundColor: isDark.value ? 'rgba(50, 50, 50, 0.9)' : 'rgba(255, 255, 255, 0.9)',
      borderColor: isDark.value ? '#626675' : '#DDD',
      textStyle: { color: textColor },
    },
    legend: {
      data: ['计划产能', '实际产能'],
      textStyle: { color: textColor, fontSize: responsivePx(12) },
      top: '5%',
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '8%',
      top: '20%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: labels,
      axisLine: { lineStyle: { color: axisLineColor } },
      axisLabel: {
        color: textColor,
        fontSize: responsivePx(12),
        rotate: 0,
      },
      splitLine: { show: false },
      axisTick: { show: false },
    },
    yAxis: {
      type: 'value',
      name: '产能',
      nameTextStyle: {
        color: textColor,
        fontSize: responsivePx(12),
        padding: [0, 0, 0, 20],
      },
      axisLine: { show: false },
      axisTick: { show: false },
      axisLabel: {
        color: textColor,
        fontSize: responsivePx(12),
        formatter: '{value}',
      },
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: splitLineColor,
        },
      },
      min: 0,
    },
    series: [
      {
        name: '计划产能',
        type: 'line',
        data: plannedData,
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: {
          color: '#1890ff',
          width: 3,
        },
        itemStyle: {
          color: '#1890ff',
          borderWidth: 2,
          borderColor: '#fff',
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(24, 144, 255, 0.3)' },
              { offset: 1, color: 'rgba(24, 144, 255, 0.05)' },
            ],
          },
        },
      },
      {
        name: '实际产能',
        type: 'line',
        data: actualData,
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: {
          color: '#52c41a',
          width: 3,
        },
        itemStyle: {
          color: '#52c41a',
          borderWidth: 2,
          borderColor: '#fff',
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(82, 196, 26, 0.3)' },
              { offset: 1, color: 'rgba(82, 196, 26, 0.05)' },
            ],
          },
        },
      },
    ],
    graphic: !hasData
      ? [{
          type: 'text',
          left: 'center',
          top: 'middle',
          style: {
            text: '暂无数据',
            fontSize: responsivePx(14),
            fill: textColor,
          },
        }]
      : undefined,
    animation: true,
    animationDuration: 1000,
    animationEasing: 'cubicOut',
  }
})
</script>

<template>
  <div class="h-full w-full">
    <e-charts
      :option="chartOption"
      autoresize
      class="h-full w-full"
    />
  </div>
</template>

<style scoped>
/* 确保图表容器有正确的尺寸 */
.w-full.h-full {
  min-height: 300px;
}
</style>
