import type { RouteRecordRaw } from 'vue-router'

export default [
  {
    path: '/dashboard/:code',
    name: 'dashboard',
    component: () => import('~/pages/dashboard/index.vue'),
    props: true,
    meta: {
      isPublic: true,
      noLayout: true,
    },
  },
  // {
  //   path: '/dashboard1/:code',
  //   name: 'dashboard1',
  //   component: () => import('~/pages/dashboard1/index.vue'),
  //   props: true,
  //   meta: {
  //     isPublic: true,
  //     noLayout: true,
  //   },
  // },

] as RouteRecordRaw[]
