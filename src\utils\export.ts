/**
 * 通用文件导出工具类
 */
export async function handleFileExport(
  fetchFn: () => Promise<Response>,
  fileName: string,
  loadingFn?: (loading: boolean) => void,
) {
  try {
    loadingFn?.(true)
    const response = await fetchFn()
    const blob = await response.blob()
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = fileName
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
  }
  finally {
    loadingFn?.(false)
  }
}
