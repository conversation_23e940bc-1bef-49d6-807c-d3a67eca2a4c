import {
  defineConfig,
  presetAttributify,
  presetIcons,
  presetUno,
  presetWebFonts,
} from 'unocss'

export default defineConfig({
  theme: {

    animation: {
      keyframes: {
        'scale-in': '{ from { transform: scale(0); }  to { transform: scale(1); } }',
      },
    },
    colors: {
      'error': 'var(--p-toast-error-color)',
      'success': 'var(--p-toast-success-color)',
      'warn': 'var(--p-button-warn-background-color)',
      'info': 'var(--p-button-info-background-color)',
      'contrast': 'var(--p-button-contrast-background-color)',
      'surface': 'var(--p-surface-500)',
      'surface-0': 'var(--p-surface-0)',
      'surface-50': 'var(--p-surface-50)',
      'surface-100': 'var(--p-surface-100)',
      'surface-200': 'var(--p-surface-200)',
      'surface-300': 'var(--p-surface-300)',
      'surface-400': 'var(--p-surface-400)',
      'surface-500': 'var(--p-surface-500)',
      'surface-600': 'var(--p-surface-600)',
      'surface-700': 'var(--p-surface-700)',
      'surface-800': 'var(--p-surface-800)',
      'surface-900': 'var(--p-surface-900)',
      'surface-950': 'var(--p-surface-950)',
      'primary': 'var(--p-primary-color)',
      'primary-contrast': 'var(--p-primary-contrast-color)',
      'primary-hover': 'var(--p-primary-hover-color)',
      'primary-active': 'var(--p-primary-active-color)',
      'highlight-background': 'var(--p-highlight-background)',
      'highlight-focus-background': 'var(--p-highlight-focus-background)',
      'highlight': 'var(--p-highlight-color)',
      'highlight-focus': 'var(--p-highlight-focus-color)',
      'mask-background': 'var(--p-mask-background)',
      'mask': 'var(--p-mask-color)',
      'text': 'var(--p-text-color)',
      'text-hover': 'var(--p-text-hover-color)',
      'muted': 'var(--p-text-muted-color)',
      'hover-muted': 'var(--p-text-hover-muted-color)',
      'content-background': 'var(--p-content-background)',
      'content-hover-background': 'var(--p-content-hover-background)',
      'content-border': 'var(--p-content-border-color)',
      'content': 'var(--p-content-color)',
      'content-hover': 'var(--p-content-hover-color)',
    },
  },

  safelist: [
    'grid-col-span-1',
    'grid-col-span-2',
    'grid-col-span-3',
    'grid-col-span-4',
    'grid-col-span-6',
    'grid-col-span-12',
    'grid-col-span-24',
  ],
  shortcuts: [

  ],
  rules: [
    ['card', { 'background': 'var(--surface-card)', 'padding': '2rem', 'margin-bottom': '2rem', 'border-radius': 'var(--content-border-radius)' }],
    ['panel', { 'background': 'var(--surface-card)', 'border-radius': 'var(--content-border-radius)' }],
    ['backface-visible', { 'backface-visibility': 'visible' }],
    ['backface-hidden', { 'backface-visibility': 'hidden' }],
    ['border-surface', { 'border-color': 'var(--p-content-border-color)' }],
    ['bg-emphasis', {
      background: 'var(--p-content-hover-background)',
      color: 'var(--p-content-hover-color)',
    }],
    ['bg-highlight', {
      background: 'var(--p-highlight-background)',
      color: 'var(--p-highlight-color)',
    }],
    ['bg-highlight-emphasis', {
      background: 'var(--p-highlight-focus-background)',
      color: 'var(--p-highlight-focus-color)',
    }],
    ['rounded-border', { 'border-radius': 'var(--p-content-border-radius)' }],
    ['text-color', { color: 'var(--p-text-color)' }],
    ['text-color-emphasis', { color: 'var(--p-text-hover-color)' }],
    ['text-muted-color', { color: 'var(--p-text-muted-color)' }],
    ['text-muted-color-emphasis', { color: 'var(--p-text-hover-muted-color)' }],
    ['bg-login', { 'background-image': 'url("~/assets/bg.jpg")', 'background-size': 'cover' }],
  ],
  outputToCssLayers: {
    cssLayerName: (internalName: string) => {
      if (internalName === 'default')
        return 'utilities'
      return internalName
    },
  },
  presets: [
    presetUno({
      // 启用将 px 转换为 rem 的功能
      pxToRem: true,

      // 设置转换的基准值。
      // UnoCSS 会用你写的 px 值除以这个基数，得到 rem 值。
      // 在你的案例中，设计稿 1rem = 14px，所以这里设置为 14。
      baseFontSize: 14,
    }),
    presetAttributify(),
    presetIcons({
      collections: {
        ic: () => import('@iconify-json/ic/icons.json').then(i => i.default),
        ri: () => import('@iconify-json/ri/icons.json').then(i => i.default),
      },
      scale: 1.2,
      warn: true,
      extraProperties: {
        'display': 'inline-block',
        'vertical-align': 'middle',
      },
    }),
    presetWebFonts({
      fonts: {
        sans: 'Noto Sans',
        serif: 'Noto Serif',
        mono: 'Noto Mono',
      },
    }),
  ],
})
